// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/category_card.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';

// Premium gradient colors for categories
List<Map<String, dynamic>> getCategoriesWithPremiumColors(
  ThemeProvider themeProvider,
) {
  final premiumColors = [
    themeProvider.premiumGradientPrimary,
    themeProvider.premiumGradientSecondary,
    themeProvider.premiumGradientAccent,
    [const Color(0xFF667eea), const Color(0xFF764ba2)], // Cosmic Fusion
    [const Color(0xFF43cea2), const Color(0xFF185a9d)], // Endless River
    [const Color(0xFF8360c3), const Color(0xFF2ebf91)], // Sunset
    [const Color(0xFFf093fb), const Color(0xFFf5576c)], // Sweet Dreams
    [const Color(0xFF4facfe), const Color(0xFF00f2fe)], // Blue Skies
    [const Color(0xFFa8edea), const Color(0xFFfed6e3)], // Soft Pastel
    [const Color(0xFFffecd2), const Color(0xFFfcb69f)], // Warm Sunset
    [const Color(0xFFd299c2), const Color(0xFFfef9d7)], // Pink Dreams
    [const Color(0xFF89f7fe), const Color(0xFF66a6ff)], // Ocean Blue
  ];

  return [
    {
      'name': 'Bold',
      'iconAsset': 'assets/icons/bold.svg',
      'gradientColors': premiumColors[0],
    },
    {
      'name': 'Bad',
      'icon': Icons.thumb_down,
      'gradientColors': premiumColors[1],
    },
    {
      'name': 'Cute',
      'iconAsset': 'assets/icons/cute.svg',
      'gradientColors': premiumColors[2],
    },
    {
      'name': 'Clever',
      'iconAsset': 'assets/icons/cleaver.svg',
      'gradientColors': premiumColors[3],
    },
    {
      'name': 'Genius',
      'iconAsset': 'assets/icons/genius.svg',
      'gradientColors': premiumColors[4],
    },
    {
      'name': 'Dirty',
      'iconAsset': 'assets/icons/dirty.svg',
      'gradientColors': premiumColors[5],
    },
    {
      'name': 'Flirty',
      'iconAsset': 'assets/icons/flirty.svg',
      'gradientColors': premiumColors[6],
    },
    {
      'name': 'Hookup',
      'iconAsset': 'assets/icons/hookup.svg',
      'gradientColors': premiumColors[7],
    },
    {
      'name': 'Romantic',
      'iconAsset': 'assets/icons/romantic.svg',
      'gradientColors': premiumColors[8],
    },
    {
      'name': 'Funny',
      'iconAsset': 'assets/icons/funny.svg',
      'gradientColors': premiumColors[9],
    },
    {
      'name': 'Nerd',
      'iconAsset': 'assets/icons/nerd.svg',
      'gradientColors': premiumColors[10],
    },
    {
      'name': 'Food',
      'iconAsset': 'assets/icons/food.svg',
      'gradientColors': premiumColors[11],
    },
  ];
}

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    // Pre-calculate categories to avoid Consumer overhead
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final categories = getCategoriesWithPremiumColors(themeProvider);

    return Scaffold(
      appBar: Custom3DAppBar(
        title: 'Charm Shots',
        actions: [
          IconButton(
            icon: const Icon(Icons.favorite),
            onPressed: () => Navigator.pushNamed(context, '/favorites'),
          ),
        ],
      ),
      drawer: const AppDrawer(),
      backgroundColor: Colors.white, // Static background for better performance
      body: Container(
        decoration: const BoxDecoration(
          // Simplified gradient for better performance
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: categories.length,
          // Optimized ListView settings for maximum performance
          physics: const BouncingScrollPhysics(),
          cacheExtent: 50, // Minimal cache for best performance
          itemExtent: 96, // Fixed height: 80 (card) + 16 (margin) = 96
          addAutomaticKeepAlives: false,
          addRepaintBoundaries: false, // Let CategoryCard handle this
          addSemanticIndexes: false,
          clipBehavior: Clip.none,
          itemBuilder: (context, index) {
            final cat = categories[index];
            return CategoryCard(
              key: ValueKey('category_${cat['name']}'),
              title: cat['name'] as String,
              icon: cat['icon'] as IconData?,
              iconAsset: cat['iconAsset'] as String?,
              gradientColors: cat['gradientColors'] as List<Color>,
              color: (cat['gradientColors'] as List<Color>).first,
              onTap: () => _navigateToLines(context, cat['name'] as String),
            );
          },
        ),
      ),
    );
  }

  void _navigateToLines(BuildContext context, String categoryName) {
    Navigator.pushNamed(
      context,
      '/lines',
      arguments: {'category': categoryName, 'language': language},
    );
  }
}
