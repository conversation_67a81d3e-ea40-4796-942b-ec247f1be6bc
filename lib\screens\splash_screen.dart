// lib/screens/splash_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../utils/animation_utils.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _logoController = AnimationController(
      duration: AnimationUtils.slowDuration,
      vsync: this,
    );

    _textController = AnimationController(
      duration: AnimationUtils.normalDuration,
      vsync: this,
    );

    // Create animations
    _logoAnimation = CurvedAnimation(
      parent: _logoController,
      curve: AnimationUtils.smoothCurve,
    );

    _textAnimation = CurvedAnimation(
      parent: _textController,
      curve: AnimationUtils.smoothCurve,
    );

    // Start animations
    _startAnimations();

    // Navigate after animations
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/language');
      }
    });
  }

  void _startAnimations() async {
    await _logoController.forward();
    await _textController.forward();
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          body: Stack(
            children: [
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Animated text
                    AnimationUtils.fadeSlideTransition(
                      animation: _textAnimation,
                      begin: const Offset(0.0, 0.5),
                      child: Text(
                        'Charm Shots',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    AnimationUtils.scaleTransition(
                      animation: _textAnimation,
                      begin: 0.0,
                      child: const Icon(
                        Icons.star,
                        size: 40,
                        color: Colors.pink,
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 40,
                left: 0,
                right: 0,
                child: Center(
                  child: Text(
                    'Charm Shots',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                      color: themeProvider.isDarkMode
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
