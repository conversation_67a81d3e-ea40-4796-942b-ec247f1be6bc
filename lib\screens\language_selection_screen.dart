// lib/screens/language_selection_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';

class LanguageSelectScreen extends StatelessWidget {
  const LanguageSelectScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: Custom3DAppBar(title: 'Charm Shot'),
          drawer: AppDrawer(),
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Hindi Language Button with Premium Colors
                  _build3DLanguageButton(
                    context: context,
                    themeProvider: themeProvider,
                    flag: '🇮🇳',
                    language: 'Hindi',
                    gradientColors: [
                      Color(0xFF614385), // Kashmir gradient - premium purple
                      Color(0xFF516395), // Kashmir gradient - premium blue
                    ],
                    onPressed: () => Navigator.pushNamed(
                      context,
                      '/categories',
                      arguments: 'Hindi',
                    ),
                  ),
                  SizedBox(height: 80), // Increased distance between boxes
                  // English Language Button with Premium Colors
                  _build3DLanguageButton(
                    context: context,
                    themeProvider: themeProvider,
                    flag: '🇬🇧',
                    language: 'English',
                    gradientColors: [
                      Color(
                        0xFF43cea2,
                      ), // Endless River gradient - premium teal
                      Color(
                        0xFF185a9d,
                      ), // Endless River gradient - premium blue
                    ],
                    onPressed: () => Navigator.pushNamed(
                      context,
                      '/categories',
                      arguments: 'English',
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _build3DLanguageButton({
    required BuildContext context,
    required ThemeProvider themeProvider,
    required String flag,
    required String language,
    required List<Color> gradientColors,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 340,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            gradientColors[0],
            gradientColors[1],
            Color.lerp(gradientColors[1], Colors.white, 0.05) ??
                gradientColors[1],
          ],
        ),
        boxShadow: [
          // Reduced shadow for cleaner premium look
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          // Subtle colored shadow
          BoxShadow(
            color: gradientColors[0].withValues(alpha: 0.2),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(32),
          onTap: onPressed,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Flag with subtle shadow
                Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(flag, style: TextStyle(fontSize: 40)),
                ),
                SizedBox(width: 16),
                // Language text with subtle shadow
                Text(
                  language,
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
