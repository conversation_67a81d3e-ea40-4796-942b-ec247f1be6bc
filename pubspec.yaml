name: charmshot1
description: "Charm Shots - A pickup lines app"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.1
  
dependencies:
  flutter:
    sdk: flutter
  share_plus: ^11.0.0
  provider: ^6.1.2
  markdown_widget: ^2.3.2+6
  google_fonts: ^6.1.0
  cupertino_icons: ^1.0.8
  screenshot: ^3.0.0
  path_provider: ^2.1.5
  cached_network_image: ^3.3.1
  flutter_cache_manager: ^3.3.1
  package_info_plus: ^8.0.0
  url_launcher: ^6.3.1
  flutter_svg: ^2.2.0
  gal: ^2.3.0
  permission_handler: ^12.0.0+1 #11.3.1
  shared_preferences: ^2.3.2
  smooth_list_view: ^2.0.2
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.4 #0.13.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/images/splash_screens/charm_logo.png

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  web:
    generate: true
    image_path: "assets/images/app_icons/app_icon.png"
    background_color: "#ffffff"
    theme_color: "#0175C2"
  image_path: "assets/images/app_icons/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
