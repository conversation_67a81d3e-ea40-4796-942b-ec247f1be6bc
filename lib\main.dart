// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui'; // Add this import for PointerDeviceKind
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'screens/language_selection_screen.dart';
import 'screens/category_screen.dart';
import 'screens/lines_list_screen.dart';
import 'screens/performance_debug_screen.dart';
import 'screens/favorites_screen.dart';
import 'utils/storage_permission_manager.dart';
import 'providers/theme_provider.dart';
import 'providers/favorites_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Optimize Flutter engine settings for better performance
  if (!kDebugMode) {
    // Disable debugging overlays in release mode
    debugPrint = (String? message, {int? wrapWidth}) {};
  }

  // Enable hardware acceleration and optimize rendering
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // Set preferred orientations for better performance
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => FavoritesProvider()),
      ],
      child: CharmShotsApp(),
    ),
  );
}

class CharmShotsApp extends StatefulWidget {
  const CharmShotsApp({super.key});

  @override
  State<CharmShotsApp> createState() => _CharmShotsAppState();
}

class _CharmShotsAppState extends State<CharmShotsApp>
    with WidgetsBindingObserver {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Minimal initialization to avoid blocking UI
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeMinimal();
    });

    // Defer heavy initialization
    Future.microtask(() => _initializeHeavyTasks());
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Quick initialization for essential features
  void _initializeMinimal() {
    try {
      context.read<FavoritesProvider>().initialize();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing favorites: $e');
      }
    }
  }

  // Heavy tasks that can be deferred
  void _initializeHeavyTasks() async {
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    try {
      // Initialize storage permissions after UI is ready
      StoragePermissionManager.instance.initialize();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing storage: $e');
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Optimize for app lifecycle changes
    switch (state) {
      case AppLifecycleState.paused:
        // Clear unnecessary caches when app goes to background
        _clearTemporaryCaches();
        break;
      case AppLifecycleState.resumed:
        // Minimal refresh when app comes back
        _refreshMinimal();
        break;
      default:
        break;
    }
  }

  void _clearTemporaryCaches() {
    // Clear image cache if memory is low
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  void _refreshMinimal() {
    if (mounted) {
      // Minimal refresh without rebuilding entire tree
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'Charm Shots',
          debugShowCheckedModeBanner: false,

          // Performance optimizations
          navigatorKey: navigatorKey,

          // Optimized theme configuration
          theme: themeProvider.currentTheme,

          // Performance-focused material app settings
          builder: (context, child) {
            return MediaQuery(
              // Disable text scaling for consistent performance
              data: MediaQuery.of(
                context,
              ).copyWith(textScaler: TextScaler.noScaling),
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: child ?? const SizedBox.shrink(),
              ),
            );
          },

          // Simplified routing for better performance
          initialRoute: '/',
          routes: _buildOptimizedRoutes(),

          // Minimal page transitions
          onGenerateRoute: (settings) => _generateOptimizedRoute(settings),

          // Optimize material app for scrolling performance
          scrollBehavior: const CustomScrollBehavior(),
        );
      },
    );
  }

  // Optimized static routes for better performance
  Map<String, WidgetBuilder> _buildOptimizedRoutes() {
    return {
      '/': (context) => const SplashScreen(),
      '/language': (context) => const LanguageSelectScreen(),
      '/performance-debug': (context) => const PerformanceDebugScreen(),
      '/favorites': (context) => const FavoritesScreen(),
    };
  }

  // Generate routes with minimal transitions
  Route<dynamic>? _generateOptimizedRoute(RouteSettings settings) {
    Widget page;

    switch (settings.name) {
      case '/categories':
        final language = settings.arguments as String? ?? 'English';
        page = CategoryScreen(language: language);
        break;

      case '/lines':
        final args = settings.arguments;
        if (args is Map<String, String>) {
          page = LinesListScreen(
            category: args['category'] ?? 'Bold',
            language: args['language'] ?? 'English',
          );
        } else {
          page = LinesListScreen(
            category: args as String? ?? 'Bold',
            language: 'English',
          );
        }
        break;

      default:
        return null;
    }

    // Ultra-fast route transitions to prevent frame drops
    return PageRouteBuilder<dynamic>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(
        milliseconds: 50,
      ), // Minimal transition time
      reverseTransitionDuration: const Duration(milliseconds: 50),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // Instant transition - no animation to prevent frame drops
        return child;

        // Alternative: Minimal fade if you want some animation
        // return FadeTransition(
        //   opacity: Tween<double>(
        //     begin: 0.95, // Start nearly opaque for faster transition
        //     end: 1.0,
        //   ).animate(CurvedAnimation(
        //     parent: animation,
        //     curve: Curves.easeOut,
        //   )),
        //   child: child,
        // );
      },
    );
  }
}

// Custom scroll behavior for optimized scrolling performance
class CustomScrollBehavior extends ScrollBehavior {
  const CustomScrollBehavior();

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    // Use platform-specific physics for best performance
    switch (getPlatform(context)) {
      case TargetPlatform.iOS:
        return const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        );
      default:
        return const ClampingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        );
    }
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Disable scrollbars for better performance on mobile
    if (Theme.of(context).platform == TargetPlatform.android ||
        Theme.of(context).platform == TargetPlatform.iOS) {
      return child;
    }
    return super.buildScrollbar(context, child, details);
  }

  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Minimal overscroll indicator for better performance
    switch (getPlatform(context)) {
      case TargetPlatform.iOS:
        return child; // iOS has no overscroll indicator
      default:
        return GlowingOverscrollIndicator(
          axisDirection: details.direction,
          color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
          child: child,
        );
    }
  }

  @override
  Set<PointerDeviceKind> get dragDevices => {
    PointerDeviceKind.touch,
    PointerDeviceKind.mouse,
    PointerDeviceKind.stylus,
    PointerDeviceKind.trackpad,
  };
}

// Global navigation service for optimized navigation
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  static BuildContext? get currentContext => navigatorKey.currentContext;

  static Future<T?> pushNamed<T extends Object?>(
    String routeName, {
    Object? arguments,
  }) {
    return navigatorKey.currentState!.pushNamed<T>(
      routeName,
      arguments: arguments,
    );
  }

  static Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    TO? result,
  }) {
    return navigatorKey.currentState!.pushReplacementNamed<T, TO>(
      routeName,
      arguments: arguments,
      result: result,
    );
  }

  static void pop<T extends Object?>([T? result]) {
    return navigatorKey.currentState!.pop<T>(result);
  }

  static bool canPop() {
    return navigatorKey.currentState!.canPop();
  }
}
