// lib/widgets/app_drawer.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../screens/performance_debug_screen.dart';
import '../screens/todays_shots_screen.dart';
import '../screens/shots_maker_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/terms_conditions_screen.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> with TickerProviderStateMixin {
  String _version = "1.0.0";

  // Animation controllers for smooth animations
  late AnimationController _drawerAnimationController;
  late AnimationController _menuAnimationController;
  late Animation<double> _menuFadeAnimation;

  // For staggered animations
  final List<AnimationController> _itemAnimationControllers = [];
  final List<Animation<double>> _itemSlideAnimations = [];
  final List<Animation<double>> _itemFadeAnimations = [];

  @override
  void initState() {
    super.initState();
    _getVersionInfo();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Main drawer animation
    _drawerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Menu items animation
    _menuAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Fade animation for menu items
    _menuFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _menuAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Create staggered animations for menu items
    const int itemCount = 8;
    for (int i = 0; i < itemCount; i++) {
      final AnimationController itemController = AnimationController(
        duration: Duration(milliseconds: 200 + (i * 50)),
        vsync: this,
      );

      final Animation<double> slideAnimation =
          Tween<double>(begin: 50.0, end: 0.0).animate(
            CurvedAnimation(parent: itemController, curve: Curves.easeOutBack),
          );

      final Animation<double> fadeAnimation =
          Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(parent: itemController, curve: Curves.easeInOut),
          );

      _itemAnimationControllers.add(itemController);
      _itemSlideAnimations.add(slideAnimation);
      _itemFadeAnimations.add(fadeAnimation);
    }

    // Start animations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAnimations();
    });
  }

  void _startAnimations() {
    _drawerAnimationController.forward();

    Future.delayed(const Duration(milliseconds: 100), () {
      _menuAnimationController.forward();

      for (int i = 0; i < _itemAnimationControllers.length; i++) {
        Future.delayed(Duration(milliseconds: i * 50), () {
          if (mounted) {
            _itemAnimationControllers[i].forward();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _drawerAnimationController.dispose();
    _menuAnimationController.dispose();
    for (final controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _getVersionInfo() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _version = "v${packageInfo.version}";
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _version = "v1.0.0";
        });
      }
    }
  }

  Future<void> _shareApp() async {
    try {
      const String appName = "Charm Shots";
      const String shareText =
          """
🌟 Check out $appName! 🌟

The ultimate pickup lines app with amazing features:
✨ Daily pickup lines
💝 Save your favorites
🎨 Create custom shots
🌙 Dark mode support

Download now and charm your way to success!

#CharmShots #PickupLines #Dating #Romance
""";

      await SharePlus.instance.share(
        ShareParams(text: shareText, subject: 'Check out $appName!'),
      );
    } catch (e) {
      await SharePlus.instance.share(
        ShareParams(
          text: "Check out Charm Shots - The ultimate pickup lines app! 🌟",
          subject: 'Check out Charm Shots!',
        ),
      );
    }
  }

  Future<void> _rateApp() async {
    const String playStoreUrl =
        "https://play.google.com/store/apps/details?id=com.example.charmshots.charmshot1";

    try {
      final Uri url = Uri.parse(playStoreUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          _showRateAppDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        _showRateAppDialog();
      }
    }
  }

  void _showRateAppDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Rate Charm Shots'),
          content: const Text(
            'We would love your feedback! Please rate us on the Google Play Store.\n\n'
            'Search for "Charm Shots" in the Play Store or visit our app page.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  // Navigation methods for each screen
  void _navigateToTodaysShots() {
    Navigator.pop(context);
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const TodaysShotsScreen(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
                  const begin = Offset(1.0, 0.0);
                  const end = Offset.zero;
                  const curve = Curves.easeInOutCubic;
                  var tween = Tween(
                    begin: begin,
                    end: end,
                  ).chain(CurveTween(curve: curve));
                  return SlideTransition(
                    position: animation.drive(tween),
                    child: child,
                  );
                },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    });
  }

  void _navigateToFavorites() {
    Navigator.pop(context);
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                FavoritesScreen(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
                  return SlideTransition(
                    position:
                        Tween<Offset>(
                          begin: const Offset(1.0, 0.0),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeInOutCubic,
                          ),
                        ),
                    child: child,
                  );
                },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    });
  }

  void _navigateToShotsMaker() {
    Navigator.pop(context);
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                ShotsMakerScreen(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
                  return SlideTransition(
                    position:
                        Tween<Offset>(
                          begin: const Offset(1.0, 0.0),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeInOutCubic,
                          ),
                        ),
                    child: child,
                  );
                },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    });
  }

  void _navigateToSettings() {
    Navigator.pop(context);
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                SettingsScreen(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
                  return SlideTransition(
                    position:
                        Tween<Offset>(
                          begin: const Offset(1.0, 0.0),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeInOutCubic,
                          ),
                        ),
                    child: child,
                  );
                },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    });
  }

  void _navigateToPrivacyPolicy() {
    Navigator.pop(context);
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
        );
      }
    });
  }

  void _navigateToTermsConditions() {
    Navigator.pop(context);
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TermsConditionsScreen(),
          ),
        );
      }
    });
  }

  void _navigateToPerformanceDebug() {
    Navigator.pop(context);
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => PerformanceDebugScreen()),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return SlideTransition(
          position:
              Tween<Offset>(
                begin: const Offset(-1.0, 0.0),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _drawerAnimationController,
                  curve: Curves.easeOutCubic,
                ),
              ),
          child: Drawer(
            backgroundColor: themeProvider.isDarkMode
                ? Colors.grey.shade800
                : Colors.white,
            elevation: 16,
            child: FadeTransition(
              opacity: _menuFadeAnimation,
              child: Column(
                children: [
                  _buildAnimatedHeader(themeProvider),
                  Expanded(child: _buildAnimatedMenuItems(themeProvider)),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedHeader(ThemeProvider themeProvider) {
    return Container(
      decoration: BoxDecoration(
        color: themeProvider.isDarkMode ? Colors.grey.shade800 : Colors.white,
        boxShadow: [
          BoxShadow(
            color: themeProvider.isDarkMode
                ? Colors.black.withValues(alpha: 0.4)
                : Colors.black.withValues(alpha: 0.12),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: themeProvider.isDarkMode
                  ? Colors.grey.shade800
                  : Colors.white,
              border: Border.all(color: Colors.transparent),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                ScaleTransition(
                  scale: Tween<double>(begin: 0.5, end: 1.0).animate(
                    CurvedAnimation(
                      parent: _drawerAnimationController,
                      curve: Curves.elasticOut,
                    ),
                  ),
                  child: Image.asset('assets/images/charm_logo.png', width: 50),
                ),
                const SizedBox(height: 8),
                SlideTransition(
                  position:
                      Tween<Offset>(
                        begin: const Offset(0.5, 0.0),
                        end: Offset.zero,
                      ).animate(
                        CurvedAnimation(
                          parent: _drawerAnimationController,
                          curve: Curves.easeOutBack,
                        ),
                      ),
                  child: Text(
                    'Charm Shots',
                    style: TextStyle(
                      fontSize: 20,
                      color: themeProvider.isDarkMode
                          ? Colors.white
                          : Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          _buildAnimatedSeparator(),
        ],
      ),
    );
  }

  Widget _buildAnimatedSeparator() {
    return AnimatedBuilder(
      animation: _menuAnimationController,
      builder: (context, child) {
        return Container(
          height: 3,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.transparent,
                Colors.grey.withValues(alpha: 0.15 * _menuFadeAnimation.value),
                Colors.grey.withValues(alpha: 0.3 * _menuFadeAnimation.value),
                Colors.grey.withValues(alpha: 0.4 * _menuFadeAnimation.value),
                Colors.grey.withValues(alpha: 0.3 * _menuFadeAnimation.value),
                Colors.grey.withValues(alpha: 0.15 * _menuFadeAnimation.value),
                Colors.transparent,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedMenuItems(ThemeProvider themeProvider) {
    return Container(
      decoration: BoxDecoration(
        color: themeProvider.isDarkMode ? Colors.grey.shade800 : Colors.white,
      ),
      child: Column(
        children: [
          const SizedBox(height: 8),

          // Main menu items
          _buildAnimatedMenuItem(
            0,
            Icons.today,
            "Today's Shots",
            _navigateToTodaysShots,
            themeProvider,
          ),

          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              return _buildAnimatedMenuItem(
                1,
                Icons.favorite,
                "Favourite (${favoritesProvider.favoritesCount})",
                _navigateToFavorites,
                themeProvider,
              );
            },
          ),

          _buildAnimatedMenuItem(
            2,
            Icons.edit,
            "Shots Maker",
            _navigateToShotsMaker,
            themeProvider,
          ),

          _buildAnimatedMenuItem(
            3,
            Icons.settings,
            "Settings",
            _navigateToSettings,
            themeProvider,
          ),

          const SizedBox(height: 16),

          // Secondary menu items
          _buildAnimatedMenuItem(
            4,
            Icons.privacy_tip,
            "Privacy Policy",
            _navigateToPrivacyPolicy,
            themeProvider,
          ),

          _buildAnimatedMenuItem(
            5,
            Icons.description,
            "Terms and Conditions",
            _navigateToTermsConditions,
            themeProvider,
          ),

          _buildAnimatedMenuItem(6, Icons.star_rate, "Rate Us", () {
            Navigator.pop(context);
            _rateApp();
          }, themeProvider),

          _buildAnimatedMenuItem(7, Icons.share, "Share App", () {
            Navigator.pop(context);
            _shareApp();
          }, themeProvider),

          // Debug section
          if (kDebugMode) ...[
            const SizedBox(height: 16),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.grey.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            _buildAnimatedMenuItem(
              8,
              Icons.bug_report,
              "Performance Debug",
              _navigateToPerformanceDebug,
              themeProvider,
              subtitle: "Debug performance issues",
              iconColor: Colors.orange,
            ),
          ],

          const Spacer(),

          // Animated version info
          AnimatedBuilder(
            animation: _menuAnimationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _menuFadeAnimation,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    _version,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: themeProvider.isDarkMode
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedMenuItem(
    int index,
    IconData icon,
    String title,
    VoidCallback onTap,
    ThemeProvider themeProvider, {
    String? subtitle,
    Color? iconColor,
  }) {
    if (index >= _itemSlideAnimations.length) {
      return _buildStaticMenuItem(
        icon,
        title,
        onTap,
        themeProvider,
        subtitle: subtitle,
        iconColor: iconColor,
      );
    }

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        return FadeTransition(
          opacity: _itemFadeAnimations[index],
          child: Transform.translate(
            offset: Offset(_itemSlideAnimations[index].value, 0),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.transparent,
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: onTap,
                  splashColor: themeProvider.isDarkMode
                      ? Colors.grey.shade600.withValues(alpha: 0.3)
                      : Colors.grey.shade200.withValues(alpha: 0.5),
                  highlightColor: themeProvider.isDarkMode
                      ? Colors.grey.shade700.withValues(alpha: 0.2)
                      : Colors.grey.shade100.withValues(alpha: 0.3),
                  child: ListTile(
                    leading: Icon(
                      icon,
                      color:
                          iconColor ??
                          (themeProvider.isDarkMode
                              ? Colors.white70
                              : Colors.black87),
                      size: 24,
                    ),
                    title: Text(
                      title,
                      style: TextStyle(
                        color: themeProvider.isDarkMode
                            ? Colors.white
                            : Colors.black87,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: subtitle != null
                        ? Text(
                            subtitle,
                            style: TextStyle(
                              color: themeProvider.isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          )
                        : null,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStaticMenuItem(
    IconData icon,
    String title,
    VoidCallback onTap,
    ThemeProvider themeProvider, {
    String? subtitle,
    Color? iconColor,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.transparent,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color:
              iconColor ??
              (themeProvider.isDarkMode ? Colors.white70 : Colors.black87),
          size: 24,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: TextStyle(
                  color: themeProvider.isDarkMode
                      ? Colors.grey.shade400
                      : Colors.grey.shade600,
                  fontSize: 12,
                ),
              )
            : null,
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        hoverColor: themeProvider.isDarkMode
            ? Colors.grey.shade700
            : Colors.grey.shade100,
        splashColor: themeProvider.isDarkMode
            ? Colors.grey.shade600
            : Colors.grey.shade200,
      ),
    );
  }
}
